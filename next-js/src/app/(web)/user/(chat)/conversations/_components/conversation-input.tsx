"use client";

import { useState, useRef, useEffect } from "react";

import { SendIcon, XCircle } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface ConversationInputProps {
  onSendMessage: (content: string) => void;
  isDisabled?: boolean;
  disabledReason?: string;
  error?: string | null;
  onClearError?: () => void;
  conversationId?: string;
  onStartTyping?: (conversationId: string) => void;
  onStopTyping?: (conversationId: string) => void;
}

export function ConversationInput({
  onSendMessage,
  isDisabled = false,
  disabledReason = "This conversation has been ended",
  error,
  onClearError,
  conversationId,
  onStartTyping,
  onStopTyping,
}: ConversationInputProps) {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleSend = async () => {
    if (message.trim() && !isDisabled && !isSending) {
      setIsSending(true);
      try {
        await onSendMessage(message.trim());
        setMessage("");
      } catch (error) {
        console.error("Failed to send message:", error);
      } finally {
        setIsSending(false);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicators
    if (conversationId && onStartTyping && onStopTyping) {
      if (value.trim() && !isTyping) {
        setIsTyping(true);
        onStartTyping(conversationId);
      }

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set new timeout to stop typing after 2 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        if (isTyping) {
          setIsTyping(false);
          onStopTyping(conversationId);
        }
      }, 2000);

      // Stop typing immediately if input is empty
      if (!value.trim() && isTyping) {
        setIsTyping(false);
        onStopTyping(conversationId);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      }
    }
  };

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  if (isDisabled) {
    return (
      <div
        className={cn(
          "p-4 border-t border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20"
        )}
      >
        <div
          className={cn(
            "flex items-center justify-center space-x-2 text-red-600 dark:text-red-400"
          )}
        >
          <XCircle className={cn("size-5")} />
          <span className={cn("text-sm font-medium")}>{disabledReason}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
      )}
    >
      {error && (
        <div
          className={cn(
            "p-3 bg-red-50 dark:bg-red-950/20 border-b border-red-200 dark:border-red-800"
          )}
        >
          <div className={cn("flex items-center justify-between")}>
            <div
              className={cn(
                "flex items-center space-x-2 text-red-600 dark:text-red-400"
              )}
            >
              <XCircle className={cn("size-4")} />
              <span className={cn("text-sm")}>{error}</span>
            </div>
            {onClearError && (
              <button
                type="button"
                onClick={onClearError}
                className={cn(
                  "text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                )}
              >
                <XCircle className={cn("size-4")} />
              </button>
            )}
          </div>
        </div>
      )}
      <div className={cn("p-4")}>
        <div className={cn("flex items-center space-x-2")}>
          <Input
            type="text"
            placeholder="Type a message..."
            className={cn(
              "flex-1 rounded-full px-4 py-2 border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500"
            )}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            disabled={isSending}
          />
          <Button
            type="button"
            variant="default-gradient"
            size="icon"
            className={cn("rounded-full")}
            disabled={!message.trim() || isSending}
            onClick={handleSend}
          >
            {isSending ? (
              <div
                className={cn(
                  "size-5 border-2 border-white border-t-transparent rounded-full animate-spin"
                )}
              />
            ) : (
              <SendIcon className={cn("size-5")} />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
