"use client";

import type { PublicConversation } from "~/lib/types";

import Link from "next/link";
import { useEffect } from "react";

import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { useChat } from "~/hooks/use-chat";
import { useAuthContext } from "~/context/auth";
import { ConversationsListItem } from "./conversations-list-item";
import { ConversationsSearch } from "./conversations-search";
import { Loader2 } from "lucide-react";

export const mockConversations: PublicConversation[] = [
  {
    id: "1",
    referenceId: "1",
    endedBy: ConversationEndedBy.ADMIN,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "1",
        name: "<PERSON>",
        pictureId: "1",
      },
      {
        id: "2",
        name: "<PERSON>",
        pictureId: "2",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "2",
    referenceId: "2",
    endedBy: ConversationEndedBy.SYSTEM,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "3",
        name: "<PERSON>",
        pictureId: "3",
      },
      {
        id: "4",
        name: "Bob <PERSON>",
        pictureId: "4",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "3",
    referenceId: "3",
    type: ConversationType.VENDOR,
    members: [
      {
        id: "5",
        name: "Charlie Davis",
        pictureId: "5",
      },
      {
        id: "6",
        name: "Dana Lee",
        pictureId: "6",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "4",
    referenceId: "4",
    type: ConversationType.LOGISTIC,
    members: [
      {
        id: "7",
        name: "Eve Adams",
        pictureId: "7",
      },
      {
        id: "8",
        name: "Frank Clark",
        pictureId: "8",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "5",
    referenceId: "5",
    type: ConversationType.LOGISTIC,
    members: [
      {
        id: "9",
        name: "Grace Miller",
        pictureId: "9",
      },
      {
        id: "10",
        name: "Henry Wilson",
        pictureId: "10",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "6",
    referenceId: "6",
    endedBy: ConversationEndedBy.SYSTEM,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "11",
        name: "Ivy Moore",
        pictureId: "11",
      },
      {
        id: "12",
        name: "Jack Taylor",
        pictureId: "12",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "7",
    referenceId: "7",
    type: ConversationType.VENDOR,
    members: [
      {
        id: "13",
        name: "Liam Johnson",
        pictureId: "13",
      },
      {
        id: "14",
        name: "Mia Williams",
        pictureId: "14",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
];

export function ConversationsList() {
  const { auth } = useAuthContext();
  const {
    conversations,
    isLoading,
    error,
    loadConversations,
    connect,
    clearError,
  } = useChat();

  useEffect(() => {
    if (auth?.accessToken) {
      connect(auth.accessToken);
      loadConversations();
    }
  }, [auth?.accessToken, connect, loadConversations]);

  if (isLoading) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center")}>
          <div className={cn("flex items-center space-x-2 text-gray-500")}>
            <Loader2 className={cn("size-4 animate-spin")} />
            <span>Loading conversations...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center p-4")}>
          <div className={cn("text-center")}>
            <p className={cn("text-red-600 dark:text-red-400 mb-2")}>{error}</p>
            <button
              type="button"
              onClick={() => {
                clearError();
                loadConversations();
              }}
              className={cn(
                "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              )}
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className={cn("flex flex-col h-full")}>
        <ConversationsSearch onSearch={() => {}} />
        <div className={cn("flex-grow flex items-center justify-center p-4")}>
          <div className={cn("text-center text-gray-500")}>
            <p>No conversations yet</p>
            <p className={cn("text-sm mt-1")}>
              Start a conversation to see it here
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationsSearch onSearch={() => {}} />
      <div className={cn("flex-grow overflow-y-auto space-y-2 p-2")}>
        {conversations.map((conversation) => (
          <Link
            key={conversation.id}
            href={routes.app.user.conversations.url(conversation.id)}
            className={cn("block")}
          >
            <ConversationsListItem conversation={conversation} />
          </Link>
        ))}
      </div>
    </div>
  );
}
