"use client";

import { use, useEffect, useState } from "react";
import { Loader2 } from "lucide-react";

import { useChat } from "~/hooks/use-chat";
import { useAuthContext } from "~/context/auth";
import { cn } from "~/lib/utils";
import { Conversation } from "../_components/conversation";

export default function ConversationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const { auth, token } = useAuthContext();
  const { conversations, messages, isLoading, error, connect, clearError } =
    useChat();

  const [currentConversation, setCurrentConversation] = useState(null);
  const [conversationMessages, setConversationMessages] = useState([]);

  useEffect(() => {
    if (token) {
      connect(token);
    }
  }, [token, connect]);

  useEffect(() => {
    const conversation = conversations.find((c) => c.id === resolvedParams.id);
    if (conversation) {
      setCurrentConversation(conversation);
      setConversationMessages(messages[conversation.id] || []);
    }
  }, [conversations, messages, resolvedParams.id]);

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center h-full")}>
        <div className={cn("flex items-center space-x-2 text-gray-500")}>
          <Loader2 className={cn("size-4 animate-spin")} />
          <span>Loading conversation...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center h-full p-4")}>
        <div className={cn("text-center")}>
          <p className={cn("text-red-600 dark:text-red-400 mb-2")}>{error}</p>
          <button
            type="button"
            onClick={clearError}
            className={cn(
              "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            )}
          >
            Dismiss
          </button>
        </div>
      </div>
    );
  }

  if (!currentConversation) {
    return (
      <div className={cn("flex items-center justify-center h-full")}>
        <div className={cn("text-center text-gray-500")}>
          <p>Conversation not found</p>
          <p className={cn("text-sm mt-1")}>
            The conversation may have been deleted or you don't have access to
            it.
          </p>
        </div>
      </div>
    );
  }

  // Get current user ID from auth
  const currentUserId = auth?.id || "";

  return (
    <Conversation
      conversation={currentConversation}
      messages={conversationMessages}
      currentUserId={currentUserId}
    />
  );
}
