export enum Role {
  UNSPECIFIED = "UNSPECIFIED",
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN = "ADMIN",
  VENDOR = "VENDOR",
  LOGISTIC = "LOGISTIC",
  USER = "USER",
}

export enum UserStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export enum OtpType {
  VERIFY = "VERIFY",
  RESET = "RESET",
}

export enum CategoryStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export enum ProductCondition {
  EXCELLENT = "EXCELLENT",
  GOOD = "GOOD",
  FAIR = "FAIR",
}

export enum OrderStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  READY = "READY",
  COMPLETED = "COMPLETED",
}

export enum DeliveryOption {
  SELF_PICKUP = "SELF_PICKUP",
  LOGISTIC = "LOGISTIC",
}

export enum DeliveryRequestStatus {
  PENDING = "PENDING",
  PROPOSED = "PROPOSED",
  PROCESSING = "PROCESSING",
  IN_TRANSIT = "IN_TRANSIT",
  DELIVERED = "DELIVERED",
}

export type PublicAuthType = {
  id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
};

export type AuthType = PublicAuthType & {
  status: UserStatus;
  role: Role;
  isVerified: boolean;
  isDeleted: boolean;
};

export type AdminProfileType = {
  id: string;
  pictureId: string;
  name: string;
  phone: string;
  createdAt: Date;
  updatedAt: Date;
};

export type VendorProfileType = {
  id: string;
  pictureId: string;
  name: string;
  description: string;
  phone: string;
  postalCode: string;
  city: string;
  pickupAddress: string;
  createdAt: Date;
  updatedAt: Date;
};

export type LogisticProviderProfileType = {
  id: string;
  pictureId: string;
  name: string;
  description: string;
  phone: string;
  postalCode: string;
  city: string;
  address: string;
  createdAt: Date;
  updatedAt: Date;
};

export type UserProfileType = {
  id: string;
  pictureId: string;
  name: string;
  phone: string;
  postalCode: string;
  city: string;
  deliveryAddress: string;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicCategoryType = {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
};

export type CategoryType = PublicCategoryType & {
  status: CategoryStatus;
  isDeleted: boolean;
};

export type PublicProductType = {
  id: string;
  pictureIds: string[];
  name: string;
  description: string;
  previousUsage?: string;
  sku: string;
  stock: number;
  price: number;
  salePrice?: number;
  condition?: ProductCondition;
  isVerified?: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type ProductType = PublicProductType & {
  isDeleted: boolean;
};

export type CartItemType = PublicProductType & {
  quantity: number;
};

export type PublicProductRequestType = {
  id: string;
  pictureIds: string[];
  name: string;
  description: string;
  quantity: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
};

export type ProductRequestType = PublicProductRequestType & {
  isDeleted: boolean;
};

export type PublicOrderToProductType = {
  id: string;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicOrderType = {
  id: string;
  totalPrice: number;
  status: OrderStatus;
  deliveryOption: DeliveryOption;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicDeliveryRequestType = {
  id: string;
  price: number;
  acceptedPrice?: number;
  status: DeliveryRequestStatus;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicLogisticProviderResponseType = {
  id: string;
  price: number;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicReviewType = {
  id: string;
  rating: number;
  comment: string;
  createdAt: Date;
  updatedAt: Date;
};

export enum ConversationType {
  VENDOR = "VENDOR",
  LOGISTIC = "LOGISTIC",
}

export enum ConversationEndedBy {
  SYSTEM = "SYSTEM",
  ADMIN = "ADMIN",
}

export type PublicMember = {
  id: string;
  authId: string;
  auth: {
    id: string;
    email: string;
    role: string;
  };
};

export type PublicConversation = {
  id: string;
  referenceId?: string;
  endedBy?: ConversationEndedBy;
  type: ConversationType;
  members: PublicMember[];
  lastMessage?: {
    id: string;
    content: string;
    isRead: boolean;
    senderId: string;
    createdAt: Date;
  };
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
};

export type PublicConversationWithMessages = PublicConversation & {
  messages: PublicMessage[];
};

export type PublicMessage = {
  id: string;
  content: string;
  isRead: boolean;
  senderId: string;
  conversationId: string;
  sender: {
    id: string;
    email: string;
  };
  createdAt: Date;
  updatedAt: Date;
};

// Socket.IO Response Types
export interface SocketResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export interface UserOnlineStatus {
  userId: string;
  email: string;
  isOnline: boolean;
  lastSeen: Date;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userEmail: string;
  isTyping: boolean;
}

export interface ConversationUpdate {
  conversationId: string;
  type: "new_message" | "messages_read" | "message_read";
  userId?: string;
  message?: PublicMessage;
  messageId?: string;
  readBy?: string;
}

export type AdminDashboardKPIsType = {
  vendorsCount: number;
  productsCount: number;
  ordersCount: number;
  usersCount: number;
  recentOrders: (PublicOrderType & {
    orderToProduct: (PublicOrderToProductType & {
      product: ProductType & {
        category: CategoryType;
        vendor: VendorProfileType;
      };
    })[];
    user: UserProfileType;
    deliveryRequest: PublicDeliveryRequestType | null;
  })[];
  recentProducts: (ProductType & {
    category: CategoryType;
    vendor: VendorProfileType;
  })[];
};

export type VendorDashboardKPIsType = {
  totalRevenue: number;
  productsCount: number;
  ordersCount: number;
  usersCount: number;
  recentOrders: (PublicOrderType & {
    orderToProduct: (PublicOrderToProductType & {
      product: ProductType & {
        category: CategoryType;
        vendor: VendorProfileType;
      };
    })[];
    user: UserProfileType;
    deliveryRequest: PublicDeliveryRequestType | null;
  })[];
  recentProducts: (ProductType & {
    category: CategoryType;
    vendor: VendorProfileType;
  })[];
};

export type SingleResponseType<T> = {
  data: T;
  info: {
    message: string;
  };
};

export type MultipleResponseType<T> = {
  data: T;
  meta: {
    total: number;
    pages: number;
    limit: number;
    page: number;
  };
  info: {
    message: string;
  };
};

export type ErrorResponseType<T> = {
  info: {
    message: string;
  };
};
