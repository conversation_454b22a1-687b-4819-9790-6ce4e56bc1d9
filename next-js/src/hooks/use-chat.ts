import { useCallback, useEffect, useState, useRef } from "react";
import { socketClient } from "~/lib/socket";
import type {
  PublicConversation,
  PublicMessage,
  UserOnlineStatus,
  TypingIndicator,
  ConversationUpdate,
  ConversationType,
} from "~/lib/types";

interface ChatState {
  conversations: PublicConversation[];
  currentConversation: PublicConversation | null;
  messages: Record<string, PublicMessage[]>;
  typingUsers: Record<string, string[]>; // conversationId -> userEmails[]
  onlineUsers: Record<string, UserOnlineStatus>;
  isLoading: boolean;
  error: string | null;
}

interface ChatActions {
  // Connection
  connect: (token: string) => void;
  disconnect: () => void;
  
  // Conversations
  loadConversations: () => Promise<void>;
  loadConversationDetails: (
    type: ConversationType,
    memberAuthId: string,
    referenceId?: string
  ) => Promise<PublicConversation>;
  setCurrentConversation: (conversation: PublicConversation | null) => void;
  
  // Messages
  sendMessage: (conversationId: string, content: string) => Promise<void>;
  markMessageAsRead: (messageId: string) => Promise<void>;
  
  // Typing
  startTyping: (conversationId: string) => Promise<void>;
  stopTyping: (conversationId: string) => Promise<void>;
  
  // User status
  getUserStatus: (userIds: string[]) => Promise<UserOnlineStatus[]>;
  
  // Utilities
  clearError: () => void;
}

export interface UseChatReturn extends ChatState, ChatActions {}

export function useChat(): UseChatReturn {
  const [state, setState] = useState<ChatState>({
    conversations: [],
    currentConversation: null,
    messages: {},
    typingUsers: {},
    onlineUsers: {},
    isLoading: false,
    error: null,
  });

  const typingTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Helper function to update state
  const updateState = useCallback((updates: Partial<ChatState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Connection methods
  const connect = useCallback((token: string) => {
    try {
      socketClient.connect(token);
      setupEventListeners();
    } catch (error) {
      updateState({ error: error instanceof Error ? error.message : "Failed to connect" });
    }
  }, []);

  const disconnect = useCallback(() => {
    socketClient.removeAllListeners();
    socketClient.disconnect();
    
    // Clear typing timeouts
    Object.values(typingTimeoutRef.current).forEach(timeout => clearTimeout(timeout));
    typingTimeoutRef.current = {};
  }, []);

  // Event listeners setup
  const setupEventListeners = useCallback(() => {
    // Message received
    socketClient.onMessageReceive(({ message, conversationId }) => {
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [conversationId]: [...(prev.messages[conversationId] || []), message],
        },
        conversations: prev.conversations.map(conv => 
          conv.id === conversationId 
            ? { ...conv, lastMessage: message, unreadCount: conv.unreadCount + 1 }
            : conv
        ),
      }));
    });

    // Typing indicators
    socketClient.onTyping((data: TypingIndicator) => {
      setState(prev => {
        const currentTyping = prev.typingUsers[data.conversationId] || [];
        let newTyping: string[];

        if (data.isTyping) {
          newTyping = currentTyping.includes(data.userEmail) 
            ? currentTyping 
            : [...currentTyping, data.userEmail];
        } else {
          newTyping = currentTyping.filter(email => email !== data.userEmail);
        }

        return {
          ...prev,
          typingUsers: {
            ...prev.typingUsers,
            [data.conversationId]: newTyping,
          },
        };
      });
    });

    // User online/offline status
    socketClient.onUserOnline(({ userId, email }) => {
      setState(prev => ({
        ...prev,
        onlineUsers: {
          ...prev.onlineUsers,
          [userId]: { userId, email, isOnline: true, lastSeen: new Date() },
        },
      }));
    });

    socketClient.onUserOffline(({ userId, email, lastSeen }) => {
      setState(prev => ({
        ...prev,
        onlineUsers: {
          ...prev.onlineUsers,
          [userId]: { userId, email, isOnline: false, lastSeen },
        },
      }));
    });

    // Conversation updates
    socketClient.onConversationUpdate((data: ConversationUpdate) => {
      if (data.type === "messages_read" || data.type === "message_read") {
        setState(prev => ({
          ...prev,
          messages: {
            ...prev.messages,
            [data.conversationId]: (prev.messages[data.conversationId] || []).map(msg => 
              data.type === "message_read" && msg.id === data.messageId
                ? { ...msg, isRead: true }
                : data.type === "messages_read" && msg.senderId !== data.userId
                ? { ...msg, isRead: true }
                : msg
            ),
          },
        }));
      }
    });

    // Message read receipts
    socketClient.onMessageRead(({ messageId, conversationId }) => {
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [conversationId]: (prev.messages[conversationId] || []).map(msg => 
            msg.id === messageId ? { ...msg, isRead: true } : msg
          ),
        },
      }));
    });
  }, []);

  // Conversation methods
  const loadConversations = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      const conversations = await socketClient.getConversations();
      updateState({ conversations, isLoading: false });
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : "Failed to load conversations",
        isLoading: false 
      });
    }
  }, [updateState]);

  const loadConversationDetails = useCallback(async (
    type: ConversationType,
    memberAuthId: string,
    referenceId?: string
  ) => {
    try {
      updateState({ isLoading: true, error: null });
      const conversation = await socketClient.getConversationDetails(type, memberAuthId, referenceId);
      
      // Update messages for this conversation
      updateState({
        messages: {
          ...state.messages,
          [conversation.id]: conversation.messages || [],
        },
        isLoading: false,
      });
      
      return conversation;
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : "Failed to load conversation details",
        isLoading: false 
      });
      throw error;
    }
  }, [state.messages, updateState]);

  const setCurrentConversation = useCallback((conversation: PublicConversation | null) => {
    updateState({ currentConversation: conversation });
  }, [updateState]);

  // Message methods
  const sendMessage = useCallback(async (conversationId: string, content: string) => {
    try {
      updateState({ error: null });
      const message = await socketClient.sendMessage(conversationId, content);
      
      // Optimistically add message to local state
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [conversationId]: [...(prev.messages[conversationId] || []), message],
        },
      }));
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : "Failed to send message" 
      });
      throw error;
    }
  }, [updateState]);

  const markMessageAsRead = useCallback(async (messageId: string) => {
    try {
      await socketClient.markMessageAsRead(messageId);
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : "Failed to mark message as read" 
      });
    }
  }, [updateState]);

  // Typing methods
  const startTyping = useCallback(async (conversationId: string) => {
    try {
      await socketClient.startTyping(conversationId);
    } catch (error) {
      console.error("Failed to start typing:", error);
    }
  }, []);

  const stopTyping = useCallback(async (conversationId: string) => {
    try {
      await socketClient.stopTyping(conversationId);
    } catch (error) {
      console.error("Failed to stop typing:", error);
    }
  }, []);

  // User status methods
  const getUserStatus = useCallback(async (userIds: string[]) => {
    try {
      return await socketClient.getUserStatus(userIds);
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : "Failed to get user status" 
      });
      return [];
    }
  }, [updateState]);

  // Utility methods
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    loadConversations,
    loadConversationDetails,
    setCurrentConversation,
    sendMessage,
    markMessageAsRead,
    startTyping,
    stopTyping,
    getUserStatus,
    clearError,
  };
}
