import type { Server as SocketServer } from "socket.io";

import type { AuthenticatedSocket } from "~/socket";

import { prisma } from "~/lib/prisma";
import { events } from "~/lib/events";

// In-memory store for online users
const onlineUsers = new Map<string, { socketId: string; lastSeen: Date }>();

interface UserStatus {
  userId: string;
  email: string;
  isOnline: boolean;
  lastSeen: Date;
}

async function handleUserOnline({
  io,
  socket,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  callback?: (response: { success: boolean; data?: UserStatus; error?: string }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("User came online", { userId: userAuth.id });

    // Add user to online users map
    onlineUsers.set(userAuth.id, {
      socketId: socket.id,
      lastSeen: new Date(),
    });

    const userStatus: UserStatus = {
      userId: userAuth.id,
      email: userAuth.email,
      isOnline: true,
      lastSeen: new Date(),
    };

    // Join user to their personal room
    socket.join(userAuth.id);

    // Get all conversations the user is part of
    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
      include: {
        members: {
          select: {
            authId: true,
          },
        },
      },
    });

    // Join all conversation rooms
    for (const conversation of conversations) {
      socket.join(conversation.id);
      
      // Notify other participants that user is online
      socket.to(conversation.id).emit(events.users.online, {
        userId: userAuth.id,
        email: userAuth.email,
        conversationId: conversation.id,
      });
    }

    // Send acknowledgment
    if (callback) {
      callback({ success: true, data: userStatus });
    }

    console.log("User online status updated successfully", { userId: userAuth.id });

  } catch (error) {
    console.error("Error handling user online:", error);
    
    if (callback) {
      callback({ 
        success: false, 
        error: error instanceof Error ? error.message : "Failed to update online status" 
      });
    }
  }
}

async function handleUserOffline({
  io,
  socket,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("User went offline", { userId: userAuth.id });

    // Remove user from online users map
    onlineUsers.delete(userAuth.id);

    // Get all conversations the user is part of
    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
      include: {
        members: {
          select: {
            authId: true,
          },
        },
      },
    });

    // Notify other participants that user is offline
    for (const conversation of conversations) {
      socket.to(conversation.id).emit(events.users.offline, {
        userId: userAuth.id,
        email: userAuth.email,
        conversationId: conversation.id,
        lastSeen: new Date(),
      });
    }

    console.log("User offline status updated successfully", { userId: userAuth.id });

  } catch (error) {
    console.error("Error handling user offline:", error);
  }
}

async function handleGetUserStatus({
  io,
  socket,
  userIds,
  callback,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  userIds: string[];
  callback?: (response: { success: boolean; data?: UserStatus[]; error?: string }) => void;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Getting user status for", { userIds, requestedBy: userAuth.id });

    // Verify the requesting user has access to these users (they share conversations)
    const sharedConversations = await prisma.conversation.findMany({
      where: {
        AND: [
          {
            members: {
              some: {
                authId: userAuth.id,
              },
            },
          },
          {
            members: {
              some: {
                authId: {
                  in: userIds,
                },
              },
            },
          },
        ],
      },
      include: {
        members: {
          include: {
            auth: {
              select: {
                id: true,
                email: true,
              },
            },
          },
        },
      },
    });

    // Get unique user IDs that the requesting user has access to
    const accessibleUserIds = new Set<string>();
    const userEmailMap = new Map<string, string>();

    for (const conversation of sharedConversations) {
      for (const member of conversation.members) {
        if (userIds.includes(member.authId)) {
          accessibleUserIds.add(member.authId);
          userEmailMap.set(member.authId, member.auth.email);
        }
      }
    }

    // Build status response
    const userStatuses: UserStatus[] = Array.from(accessibleUserIds).map(userId => {
      const onlineInfo = onlineUsers.get(userId);
      return {
        userId,
        email: userEmailMap.get(userId) || '',
        isOnline: !!onlineInfo,
        lastSeen: onlineInfo?.lastSeen || new Date(),
      };
    });

    if (callback) {
      callback({ success: true, data: userStatuses });
    }

    console.log("User status retrieved successfully", { count: userStatuses.length });

  } catch (error) {
    console.error("Error getting user status:", error);
    
    if (callback) {
      callback({ 
        success: false, 
        error: error instanceof Error ? error.message : "Failed to get user status" 
      });
    }
  }
}

// Utility function to get online users count
function getOnlineUsersCount(): number {
  return onlineUsers.size;
}

// Utility function to check if user is online
function isUserOnline(userId: string): boolean {
  return onlineUsers.has(userId);
}

// Utility function to get all online users
function getOnlineUsers(): string[] {
  return Array.from(onlineUsers.keys());
}

export {
  handleUserOnline,
  handleUserOffline,
  handleGetUserStatus,
  getOnlineUsersCount,
  isUserOnline,
  getOnlineUsers,
  onlineUsers,
};
